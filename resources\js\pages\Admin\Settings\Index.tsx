import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm, usePage } from '@inertiajs/react';
import { AlertCircle, CheckCircle, Loader2, Save, Settings } from 'lucide-react';
import { FormEventHandler } from 'react';

interface ContactInfo {
    phone: string;
    whatsapp: string;
    email: string;
    address: string;
    postal_code: string;
    maps_link: string;
}

interface VillageProfile {
    name: string;
    district: string;
    regency: string;
    province: string;
    established_year: string;
    area: string;
    population: string;
}

interface OperatingHours {
    weekdays: string;
    saturday: string;
    sunday: string;
    break: string;
    holidays: string;
}

interface EmergencyContact {
    title: string;
    phone: string;
}

interface EmergencyContacts {
    village_head: EmergencyContact;
    village_secretary: EmergencyContact;
    security: EmergencyContact;
    health_center: EmergencyContact;
}

interface ServiceSettings {
    online_services_available: boolean;
    mobile_optimized: boolean;
    emergency_contact_available: boolean;
    multilingual_support: boolean;
    digital_signature_enabled: boolean;
}

interface SettingsData {
    'village.contact_info': ContactInfo;
    'village.profile': VillageProfile;
    'village.operating_hours': OperatingHours;
    'village.emergency_contacts': EmergencyContacts;
    'village.service_settings': ServiceSettings;
}

interface Props {
    settings: SettingsData;
}

interface PageProps extends Record<string, unknown> {
    flash: {
        message?: string;
        success?: string;
        error?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Pengaturan',
        href: '/admin/settings',
    },
];

export default function SettingsIndex({ settings }: Props) {
    const { flash } = usePage<PageProps>().props;
    const { data, setData, put, processing, errors, clearErrors } = useForm({
        village_contact_info_phone: settings['village.contact_info']?.phone || '',
        village_contact_info_whatsapp: settings['village.contact_info']?.whatsapp || '',
        village_contact_info_email: settings['village.contact_info']?.email || '',
        village_contact_info_address: settings['village.contact_info']?.address || '',
        village_contact_info_postal_code: settings['village.contact_info']?.postal_code || '',
        village_contact_info_maps_link: settings['village.contact_info']?.maps_link || '',
        village_profile_name: settings['village.profile']?.name || '',
        village_profile_district: settings['village.profile']?.district || '',
        village_profile_regency: settings['village.profile']?.regency || '',
        village_profile_province: settings['village.profile']?.province || '',
        village_profile_established_year: settings['village.profile']?.established_year || '',
        village_profile_area: settings['village.profile']?.area || '',
        village_profile_population: settings['village.profile']?.population || '',
        village_operating_hours_weekdays: settings['village.operating_hours']?.weekdays || '',
        village_operating_hours_saturday: settings['village.operating_hours']?.saturday || '',
        village_operating_hours_sunday: settings['village.operating_hours']?.sunday || '',
        village_operating_hours_break: settings['village.operating_hours']?.break || '',
        village_operating_hours_holidays: settings['village.operating_hours']?.holidays || '',
        village_emergency_contacts_village_head_title: settings['village.emergency_contacts']?.village_head?.title || '',
        village_emergency_contacts_village_head_phone: settings['village.emergency_contacts']?.village_head?.phone || '',
        village_emergency_contacts_village_secretary_title: settings['village.emergency_contacts']?.village_secretary?.title || '',
        village_emergency_contacts_village_secretary_phone: settings['village.emergency_contacts']?.village_secretary?.phone || '',
        village_emergency_contacts_security_title: settings['village.emergency_contacts']?.security?.title || '',
        village_emergency_contacts_security_phone: settings['village.emergency_contacts']?.security?.phone || '',
        village_emergency_contacts_health_center_title: settings['village.emergency_contacts']?.health_center?.title || '',
        village_emergency_contacts_health_center_phone: settings['village.emergency_contacts']?.health_center?.phone || '',
    });

    const handleSubmit: FormEventHandler = (e) => {
        e.preventDefault();
        put(route('admin.settings.update'), {
            onSuccess: () => {
                // Success message will be handled via flash message
            },
            onError: () => {
                // Errors are automatically handled by Inertia
            },
        });
    };

    // Clear individual field errors when user starts typing
    const handleFieldChange = (field: keyof typeof data, value: string) => {
        setData(field, value);
        if (errors[field as keyof typeof errors]) {
            clearErrors(field as keyof typeof errors);
        }
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Pengaturan - Desa Lemah Duhur" />

            <div className="flex h-full flex-1 flex-col gap-6 bg-gray-50 p-6 dark:bg-gray-950">
                {/* Header */}
                <div className="flex flex-col gap-2">
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Pengaturan Desa</h1>
                    <p className="text-gray-600 dark:text-gray-300">Kelola informasi dasar dan pengaturan sistem untuk Website Desa Lemah Duhur</p>
                </div>

                {/* Success Message */}
                {(flash.success || flash.message) && (
                    <div className="flex items-center gap-3 rounded-lg bg-green-50 p-4 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                        <CheckCircle className="h-5 w-5 flex-shrink-0" />
                        <div>
                            <p className="font-medium">Berhasil!</p>
                            <p className="text-sm">{flash.success || flash.message}</p>
                        </div>
                    </div>
                )}

                {/* Error Message */}
                {flash.error && (
                    <div className="flex items-center gap-3 rounded-lg bg-red-50 p-4 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                        <AlertCircle className="h-5 w-5 flex-shrink-0" />
                        <div>
                            <p className="font-medium">Terjadi Kesalahan!</p>
                            <p className="text-sm">{flash.error}</p>
                        </div>
                    </div>
                )}

                {/* General Form Errors */}
                {Object.keys(errors).length > 0 && (
                    <div className="flex items-start gap-3 rounded-lg bg-red-50 p-4 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                        <AlertCircle className="mt-0.5 h-5 w-5 flex-shrink-0" />
                        <div>
                            <p className="font-medium">Mohon perbaiki kesalahan berikut:</p>
                            <ul className="mt-2 space-y-1 text-sm">
                                {Object.entries(errors)
                                    .slice(0, 3)
                                    .map(([field, message]) => (
                                        <li key={field}>• {message}</li>
                                    ))}
                                {Object.keys(errors).length > 3 && <li>• Dan {Object.keys(errors).length - 3} kesalahan lainnya</li>}
                            </ul>
                        </div>
                    </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Settings Form Cards */}
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                        {/* Contact Information Form */}
                        <Card className="dark:bg-gray-800">
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <Settings className="h-5 w-5 text-blue-600" />
                                    <span>Informasi Kontak</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="phone">Telepon</Label>
                                        <Input
                                            id="phone"
                                            type="tel"
                                            value={data.village_contact_info_phone}
                                            onChange={(e) => handleFieldChange('village_contact_info_phone', e.target.value)}
                                            placeholder="0"
                                            className={
                                                errors.village_contact_info_phone ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_contact_info_phone && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_contact_info_phone}</p>
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="whatsapp">WhatsApp</Label>
                                        <Input
                                            id="whatsapp"
                                            type="tel"
                                            value={data.village_contact_info_whatsapp}
                                            onChange={(e) => handleFieldChange('village_contact_info_whatsapp', e.target.value)}
                                            placeholder="0"
                                            className={
                                                errors.village_contact_info_whatsapp ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_contact_info_whatsapp && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_contact_info_whatsapp}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <div>
                                    <Label htmlFor="email">Email</Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        value={data.village_contact_info_email}
                                        onChange={(e) => handleFieldChange('village_contact_info_email', e.target.value)}
                                        placeholder="<EMAIL>"
                                        className={errors.village_contact_info_email ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                                        disabled={processing}
                                    />
                                    {errors.village_contact_info_email && (
                                        <div className="mt-1 flex items-center gap-1">
                                            <AlertCircle className="h-4 w-4 text-red-500" />
                                            <p className="text-sm text-red-600">{errors.village_contact_info_email}</p>
                                        </div>
                                    )}
                                </div>
                                <div>
                                    <Label htmlFor="address">Alamat</Label>
                                    <Textarea
                                        id="address"
                                        value={data.village_contact_info_address}
                                        onChange={(e) => handleFieldChange('village_contact_info_address', e.target.value)}
                                        placeholder="Kantor Desa Lemah Duhur, Kec. Caringin, Kab. Bogor, Jawa Barat"
                                        className={
                                            errors.village_contact_info_address ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                                        }
                                        rows={3}
                                        disabled={processing}
                                    />
                                    {errors.village_contact_info_address && (
                                        <div className="mt-1 flex items-center gap-1">
                                            <AlertCircle className="h-4 w-4 text-red-500" />
                                            <p className="text-sm text-red-600">{errors.village_contact_info_address}</p>
                                        </div>
                                    )}
                                </div>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="postal_code">Kode Pos</Label>
                                        <Input
                                            id="postal_code"
                                            type="text"
                                            value={data.village_contact_info_postal_code}
                                            onChange={(e) => handleFieldChange('village_contact_info_postal_code', e.target.value)}
                                            placeholder="16730"
                                            className={
                                                errors.village_contact_info_postal_code
                                                    ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                                                    : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_contact_info_postal_code && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_contact_info_postal_code}</p>
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="maps_link">Link Google Maps</Label>
                                        <Input
                                            id="maps_link"
                                            type="url"
                                            value={data.village_contact_info_maps_link}
                                            onChange={(e) => handleFieldChange('village_contact_info_maps_link', e.target.value)}
                                            placeholder="https://maps.app.goo.gl/..."
                                            className={
                                                errors.village_contact_info_maps_link ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_contact_info_maps_link && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_contact_info_maps_link}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Village Profile Form */}
                        <Card className="dark:bg-gray-800">
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <Settings className="h-5 w-5 text-green-600" />
                                    <span>Profil Desa</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label htmlFor="village_name">Nama Desa</Label>
                                    <Input
                                        id="village_name"
                                        type="text"
                                        value={data.village_profile_name}
                                        onChange={(e) => handleFieldChange('village_profile_name', e.target.value)}
                                        placeholder="Desa Lemah Duhur"
                                        className={errors.village_profile_name ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                                        disabled={processing}
                                    />
                                    {errors.village_profile_name && (
                                        <div className="mt-1 flex items-center gap-1">
                                            <AlertCircle className="h-4 w-4 text-red-500" />
                                            <p className="text-sm text-red-600">{errors.village_profile_name}</p>
                                        </div>
                                    )}
                                </div>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                    <div>
                                        <Label htmlFor="district">Kecamatan</Label>
                                        <Input
                                            id="district"
                                            type="text"
                                            value={data.village_profile_district}
                                            onChange={(e) => handleFieldChange('village_profile_district', e.target.value)}
                                            placeholder="Caringin"
                                            className={
                                                errors.village_profile_district ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_profile_district && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_profile_district}</p>
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="regency">Kabupaten</Label>
                                        <Input
                                            id="regency"
                                            type="text"
                                            value={data.village_profile_regency}
                                            onChange={(e) => handleFieldChange('village_profile_regency', e.target.value)}
                                            placeholder="Bogor"
                                            className={errors.village_profile_regency ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                                            disabled={processing}
                                        />
                                        {errors.village_profile_regency && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_profile_regency}</p>
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="province">Provinsi</Label>
                                        <Input
                                            id="province"
                                            type="text"
                                            value={data.village_profile_province}
                                            onChange={(e) => handleFieldChange('village_profile_province', e.target.value)}
                                            placeholder="Jawa Barat"
                                            className={
                                                errors.village_profile_province ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_profile_province && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_profile_province}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                                    <div>
                                        <Label htmlFor="established_year">Tahun Berdiri</Label>
                                        <Input
                                            id="established_year"
                                            type="text"
                                            value={data.village_profile_established_year}
                                            onChange={(e) => handleFieldChange('village_profile_established_year', e.target.value)}
                                            placeholder="1910-1920"
                                            className={
                                                errors.village_profile_established_year
                                                    ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                                                    : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_profile_established_year && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_profile_established_year}</p>
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="area">Luas Wilayah</Label>
                                        <Input
                                            id="area"
                                            type="text"
                                            value={data.village_profile_area}
                                            onChange={(e) => handleFieldChange('village_profile_area', e.target.value)}
                                            placeholder="500 Ha"
                                            className={errors.village_profile_area ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                                            disabled={processing}
                                        />
                                        {errors.village_profile_area && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_profile_area}</p>
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="population">Jumlah Penduduk</Label>
                                        <Input
                                            id="population"
                                            type="text"
                                            value={data.village_profile_population}
                                            onChange={(e) => handleFieldChange('village_profile_population', e.target.value)}
                                            placeholder="5,000 jiwa"
                                            className={
                                                errors.village_profile_population ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_profile_population && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_profile_population}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Operating Hours Form */}
                        <Card className="dark:bg-gray-800">
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <Settings className="h-5 w-5 text-purple-600" />
                                    <span>Jam Operasional</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label htmlFor="weekdays">Hari Kerja (Senin - Jumat)</Label>
                                    <Input
                                        id="weekdays"
                                        type="text"
                                        value={data.village_operating_hours_weekdays}
                                        onChange={(e) => handleFieldChange('village_operating_hours_weekdays', e.target.value)}
                                        placeholder="08:00 - 16:00 WIB"
                                        className={
                                            errors.village_operating_hours_weekdays ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                                        }
                                        disabled={processing}
                                    />
                                    {errors.village_operating_hours_weekdays && (
                                        <div className="mt-1 flex items-center gap-1">
                                            <AlertCircle className="h-4 w-4 text-red-500" />
                                            <p className="text-sm text-red-600">{errors.village_operating_hours_weekdays}</p>
                                        </div>
                                    )}
                                </div>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="saturday">Sabtu</Label>
                                        <Input
                                            id="saturday"
                                            type="text"
                                            value={data.village_operating_hours_saturday}
                                            onChange={(e) => handleFieldChange('village_operating_hours_saturday', e.target.value)}
                                            placeholder="08:00 - 12:00 WIB"
                                            className={
                                                errors.village_operating_hours_saturday
                                                    ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                                                    : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_operating_hours_saturday && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_operating_hours_saturday}</p>
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="sunday">Minggu</Label>
                                        <Input
                                            id="sunday"
                                            type="text"
                                            value={data.village_operating_hours_sunday}
                                            onChange={(e) => handleFieldChange('village_operating_hours_sunday', e.target.value)}
                                            placeholder="Tutup"
                                            className={
                                                errors.village_operating_hours_sunday ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_operating_hours_sunday && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_operating_hours_sunday}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="break">Jam Istirahat</Label>
                                        <Input
                                            id="break"
                                            type="text"
                                            value={data.village_operating_hours_break}
                                            onChange={(e) => handleFieldChange('village_operating_hours_break', e.target.value)}
                                            placeholder="12:00 - 13:00 WIB"
                                            className={
                                                errors.village_operating_hours_break ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_operating_hours_break && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_operating_hours_break}</p>
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="holidays">Hari Libur</Label>
                                        <Input
                                            id="holidays"
                                            type="text"
                                            value={data.village_operating_hours_holidays}
                                            onChange={(e) => handleFieldChange('village_operating_hours_holidays', e.target.value)}
                                            placeholder="Hari libur nasional"
                                            className={
                                                errors.village_operating_hours_holidays
                                                    ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                                                    : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_operating_hours_holidays && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_operating_hours_holidays}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Emergency Contacts Form */}
                        <Card className="dark:bg-gray-800">
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <Settings className="h-5 w-5 text-red-600" />
                                    <span>Kontak Darurat</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {/* Village Head */}
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="village_head_title">Jabatan Kepala Desa</Label>
                                        <Input
                                            id="village_head_title"
                                            type="text"
                                            value={data.village_emergency_contacts_village_head_title}
                                            onChange={(e) => handleFieldChange('village_emergency_contacts_village_head_title', e.target.value)}
                                            placeholder="Kepala Desa"
                                            className={
                                                errors.village_emergency_contacts_village_head_title
                                                    ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                                                    : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_emergency_contacts_village_head_title && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_emergency_contacts_village_head_title}</p>
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="village_head_phone">Nomor Telepon Kepala Desa</Label>
                                        <Input
                                            id="village_head_phone"
                                            type="tel"
                                            value={data.village_emergency_contacts_village_head_phone}
                                            onChange={(e) => handleFieldChange('village_emergency_contacts_village_head_phone', e.target.value)}
                                            placeholder="081234567891"
                                            className={
                                                errors.village_emergency_contacts_village_head_phone
                                                    ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                                                    : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_emergency_contacts_village_head_phone && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_emergency_contacts_village_head_phone}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Village Secretary */}
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="village_secretary_title">Jabatan Sekretaris Desa</Label>
                                        <Input
                                            id="village_secretary_title"
                                            type="text"
                                            value={data.village_emergency_contacts_village_secretary_title}
                                            onChange={(e) => handleFieldChange('village_emergency_contacts_village_secretary_title', e.target.value)}
                                            placeholder="Sekretaris Desa"
                                            className={
                                                errors.village_emergency_contacts_village_secretary_title
                                                    ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                                                    : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_emergency_contacts_village_secretary_title && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_emergency_contacts_village_secretary_title}</p>
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="village_secretary_phone">Nomor Telepon Sekretaris Desa</Label>
                                        <Input
                                            id="village_secretary_phone"
                                            type="tel"
                                            value={data.village_emergency_contacts_village_secretary_phone}
                                            onChange={(e) => handleFieldChange('village_emergency_contacts_village_secretary_phone', e.target.value)}
                                            placeholder="081234567892"
                                            className={
                                                errors.village_emergency_contacts_village_secretary_phone
                                                    ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                                                    : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_emergency_contacts_village_secretary_phone && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_emergency_contacts_village_secretary_phone}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Security */}
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="security_title">Jabatan Keamanan Desa</Label>
                                        <Input
                                            id="security_title"
                                            type="text"
                                            value={data.village_emergency_contacts_security_title}
                                            onChange={(e) => handleFieldChange('village_emergency_contacts_security_title', e.target.value)}
                                            placeholder="Keamanan Desa (Hansip)"
                                            className={
                                                errors.village_emergency_contacts_security_title
                                                    ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                                                    : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_emergency_contacts_security_title && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_emergency_contacts_security_title}</p>
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="security_phone">Nomor Telepon Keamanan Desa</Label>
                                        <Input
                                            id="security_phone"
                                            type="tel"
                                            value={data.village_emergency_contacts_security_phone}
                                            onChange={(e) => handleFieldChange('village_emergency_contacts_security_phone', e.target.value)}
                                            placeholder="081234567893"
                                            className={
                                                errors.village_emergency_contacts_security_phone
                                                    ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                                                    : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_emergency_contacts_security_phone && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_emergency_contacts_security_phone}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Health Center */}
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="health_center_title">Nama Puskesmas</Label>
                                        <Input
                                            id="health_center_title"
                                            type="text"
                                            value={data.village_emergency_contacts_health_center_title}
                                            onChange={(e) => handleFieldChange('village_emergency_contacts_health_center_title', e.target.value)}
                                            placeholder="Puskesmas Caringin"
                                            className={
                                                errors.village_emergency_contacts_health_center_title
                                                    ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                                                    : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_emergency_contacts_health_center_title && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_emergency_contacts_health_center_title}</p>
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="health_center_phone">Nomor Telepon Puskesmas</Label>
                                        <Input
                                            id="health_center_phone"
                                            type="tel"
                                            value={data.village_emergency_contacts_health_center_phone}
                                            onChange={(e) => handleFieldChange('village_emergency_contacts_health_center_phone', e.target.value)}
                                            placeholder="(0251) 8240456"
                                            className={
                                                errors.village_emergency_contacts_health_center_phone
                                                    ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                                                    : ''
                                            }
                                            disabled={processing}
                                        />
                                        {errors.village_emergency_contacts_health_center_phone && (
                                            <div className="mt-1 flex items-center gap-1">
                                                <AlertCircle className="h-4 w-4 text-red-500" />
                                                <p className="text-sm text-red-600">{errors.village_emergency_contacts_health_center_phone}</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end">
                        <Button type="submit" disabled={processing} className="flex min-w-[160px] items-center space-x-2">
                            {processing ? (
                                <>
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span>Menyimpan...</span>
                                </>
                            ) : (
                                <>
                                    <Save className="h-4 w-4" />
                                    <span>Simpan Pengaturan</span>
                                </>
                            )}
                        </Button>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
