import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AdminLayout from '@/layouts/AdminLayout';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Edit, Mail, User, Calendar, Shield, UserCheck, UserX, Trash2 } from 'lucide-react';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { useState } from 'react';

interface User {
    id: number;
    name: string;
    email: string;
    role: 'admin' | 'user';
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
}

interface Props {
    user: User;
}

const breadcrumbs = [
    {
        title: 'Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Manajemen Pengguna',
        href: '/admin/users',
    },
    {
        title: 'Detail Pengguna',
        href: '#',
    },
];

export default function ShowUser({ user }: Props) {
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const { post: deleteUser, processing: deleting } = useForm();
    const { post: toggleRole, processing: toggling } = useForm();

    const handleDelete = () => {
        deleteUser(route('admin.users.destroy', user.id), {
            onSuccess: () => {
                setShowDeleteDialog(false);
            },
        });
    };

    const handleToggleRole = () => {
        toggleRole(route('admin.users.toggle-role', user.id));
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title={`${user.name} - Admin Desa Lemah Duhur`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{user.name}</h1>
                        <p className="text-gray-600 dark:text-gray-300">Detail informasi pengguna</p>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Link href={route('admin.users.edit', user.id)}>
                            <Button className="flex items-center space-x-2">
                                <Edit className="h-4 w-4" />
                                <span>Edit Pengguna</span>
                            </Button>
                        </Link>
                        <Link href={route('admin.users.index')}>
                            <Button variant="outline" className="flex items-center space-x-2">
                                <ArrowLeft className="h-4 w-4" />
                                <span>Kembali</span>
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Main Information */}
                    <div className="lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <User className="h-5 w-5" />
                                    <span>Informasi Pengguna</span>
                                </CardTitle>
                                <CardDescription>
                                    Detail lengkap informasi pengguna
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* Basic Info */}
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <div className="flex items-center space-x-2 text-sm font-medium text-gray-500">
                                            <User className="h-4 w-4" />
                                            <span>Nama Lengkap</span>
                                        </div>
                                        <p className="text-lg font-semibold">{user.name}</p>
                                    </div>

                                    <div className="space-y-2">
                                        <div className="flex items-center space-x-2 text-sm font-medium text-gray-500">
                                            <Mail className="h-4 w-4" />
                                            <span>Alamat Email</span>
                                        </div>
                                        <p className="text-lg">{user.email}</p>
                                    </div>

                                    <div className="space-y-2">
                                        <div className="flex items-center space-x-2 text-sm font-medium text-gray-500">
                                            <Shield className="h-4 w-4" />
                                            <span>Peran</span>
                                        </div>
                                        <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                                            {user.role === 'admin' ? 'Administrator' : 'User'}
                                        </Badge>
                                    </div>

                                    <div className="space-y-2">
                                        <div className="flex items-center space-x-2 text-sm font-medium text-gray-500">
                                            <Calendar className="h-4 w-4" />
                                            <span>Status Email</span>
                                        </div>
                                        <Badge variant={user.email_verified_at ? 'default' : 'destructive'}>
                                            {user.email_verified_at ? 'Terverifikasi' : 'Belum Terverifikasi'}
                                        </Badge>
                                    </div>
                                </div>

                                {/* Timestamps */}
                                <div className="border-t pt-4">
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <div className="flex items-center space-x-2 text-sm font-medium text-gray-500">
                                                <Calendar className="h-4 w-4" />
                                                <span>Dibuat</span>
                                            </div>
                                            <p className="text-sm text-gray-600">{formatDate(user.created_at)}</p>
                                        </div>

                                        <div className="space-y-2">
                                            <div className="flex items-center space-x-2 text-sm font-medium text-gray-500">
                                                <Calendar className="h-4 w-4" />
                                                <span>Terakhir Diperbarui</span>
                                            </div>
                                            <p className="text-sm text-gray-600">{formatDate(user.updated_at)}</p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Actions */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Aksi</CardTitle>
                                <CardDescription>
                                    Kelola pengguna ini
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                {/* Toggle Role */}
                                <Button
                                    variant="outline"
                                    className="w-full justify-start"
                                    onClick={handleToggleRole}
                                    disabled={toggling}
                                >
                                    {user.role === 'admin' ? (
                                        <>
                                            <UserX className="mr-2 h-4 w-4" />
                                            {toggling ? 'Mengubah...' : 'Jadikan User'}
                                        </>
                                    ) : (
                                        <>
                                            <UserCheck className="mr-2 h-4 w-4" />
                                            {toggling ? 'Mengubah...' : 'Jadikan Admin'}
                                        </>
                                    )}
                                </Button>

                                {/* Delete User */}
                                <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                                    <AlertDialogTrigger asChild>
                                        <Button variant="destructive" className="w-full justify-start">
                                            <Trash2 className="mr-2 h-4 w-4" />
                                            Hapus Pengguna
                                        </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                        <AlertDialogHeader>
                                            <AlertDialogTitle>Hapus Pengguna</AlertDialogTitle>
                                            <AlertDialogDescription>
                                                Apakah Anda yakin ingin menghapus pengguna <strong>{user.name}</strong>? 
                                                Tindakan ini tidak dapat dibatalkan.
                                            </AlertDialogDescription>
                                        </AlertDialogHeader>
                                        <AlertDialogFooter>
                                            <AlertDialogCancel>Batal</AlertDialogCancel>
                                            <AlertDialogAction
                                                onClick={handleDelete}
                                                disabled={deleting}
                                                className="bg-red-600 hover:bg-red-700"
                                            >
                                                {deleting ? 'Menghapus...' : 'Hapus'}
                                            </AlertDialogAction>
                                        </AlertDialogFooter>
                                    </AlertDialogContent>
                                </AlertDialog>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
