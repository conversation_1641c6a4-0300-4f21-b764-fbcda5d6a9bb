[2025-07-31 04:41:09] local.ERROR: Unable to locate file in Vite manifest: resources/js/pages/Test/DatePickerTest.tsx. (View: E:\Coding\PHP\Laravel\desa-lemah-duhur\resources\views\app.blade.php) {"userId":1,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Unable to locate file in Vite manifest: resources/js/pages/Test/DatePickerTest.tsx. (View: E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\resources\\views\\app.blade.php) at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:988)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Foundation\\ViteException), 1)
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\Coding\\\\PHP\\\\L...', Array)
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\Coding\\\\PHP\\\\L...', Array)
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 200, Array)
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view('app', Array)
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(135): Illuminate\\Support\\Facades\\Facade::__callStatic('view', Array)
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(907): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(892): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleErrors.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleErrors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\AddPerformanceHeaders.php(18): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AddPerformanceHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#45 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#46 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#60 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#62 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#64 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#68 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Coding\\\\PHP\\\\L...')
#69 {main}

[previous exception] [object] (Illuminate\\Foundation\\ViteException(code: 0): Unable to locate file in Vite manifest: resources/js/pages/Test/DatePickerTest.tsx. at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php:988)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Vite.php(390): Illuminate\\Foundation\\Vite->chunk(Array, 'resources/js/pa...')
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\storage\\framework\\views\\327bd22af04e861f6a177174f1e8f2f1.php(43): Illuminate\\Foundation\\Vite->__invoke(Object(Illuminate\\Support\\Collection))
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('E:\\\\Coding\\\\PHP\\\\L...')
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::{closure:Illuminate\\Filesystem\\Filesystem::getRequire():120}()
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\Coding\\\\PHP\\\\L...', Array)
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\Coding\\\\PHP\\\\L...', Array)
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\Coding\\\\PHP\\\\L...', Array)
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(61): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(91): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 200, Array)
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\ResponseFactory->view('app', Array)
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(135): Illuminate\\Support\\Facades\\Facade::__callStatic('view', Array)
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(907): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(892): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#19 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleErrors.php(20): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleErrors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\AddPerformanceHeaders.php(18): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AddPerformanceHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#36 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#37 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#41 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#45 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#46 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#47 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#48 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#49 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#50 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#60 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#62 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#64 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#66 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#68 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#72 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Coding\\\\PHP\\\\L...')
#73 {main}
"} 
[2025-08-02 02:37:25] local.ERROR: syntax error, unexpected token "{", expecting ")" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"{\", expecting \")\" at E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\routes\\web.php:155)
[stacktrace]
#0 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(512): Illuminate\\Routing\\RouteFileRegistrar->register('E:\\\\Coding\\\\PHP\\\\L...')
#1 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes('E:\\\\Coding\\\\PHP\\\\L...')
#2 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group(Array, 'E:\\\\Coding\\\\PHP\\\\L...')
#3 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(248): Illuminate\\Routing\\RouteRegistrar->group('E:\\\\Coding\\\\PHP\\\\L...')
#4 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Configuration\\ApplicationBuilder->{closure:Illuminate\\Foundation\\Configuration\\ApplicationBuilder::buildRoutingCallback():205}()
#5 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#6 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#8 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(780): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#9 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(162): Illuminate\\Container\\Container->call(Object(Closure))
#10 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(59): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#11 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->{closure:Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider::register():53}()
#12 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#13 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(780): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(143): Illuminate\\Container\\Container->call(Object(Closure))
#17 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1153): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#18 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->{closure:Illuminate\\Foundation\\Application::boot():1130}(Object(Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider), 'Illuminate\\\\Foun...')
#20 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#21 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#25 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 E:\\Coding\\PHP\\Laravel\\desa-lemah-duhur\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#28 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('E:\\\\Coding\\\\PHP\\\\L...')
#29 {main}
"} 
