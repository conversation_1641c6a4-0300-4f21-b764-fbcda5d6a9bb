import SEOHead from '@/components/Common/SEOHead';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useSettings } from '@/hooks/useSettings';
import PublicLayout from '@/layouts/PublicLayout';
import { ProfileSection } from '@/types';
import { Building2, Calendar, Globe, History, Mail, MapPin, Phone, Target, Users } from 'lucide-react';

interface ProfileProps {
    history: ProfileSection[];
    visionMission: ProfileSection[];
    demographics: ProfileSection[];
    organization: ProfileSection[];
    geography: ProfileSection[];
    seoMeta?: {
        title: string;
        description: string;
        image?: string;
        url?: string;
        type?: string;
        siteName?: string;
        locale?: string;
    };
    structuredData?: object;
}

export default function Profile({ history, visionMission, demographics, organization, geography, seoMeta, structuredData }: ProfileProps) {
    const { getVillageProfile, getContactInfo, getOperatingHours } = useSettings();
    const renderContent = (content: string) => {
        return <div className="prose prose-gray dark:prose-invert max-w-none" dangerouslySetInnerHTML={{ __html: content }} />;
    };

    const renderImageWithContent = (section: ProfileSection) => {
        return (
            <div className="grid grid-cols-1 items-start gap-4 sm:gap-6 lg:grid-cols-2 lg:gap-8">
                {section.image && (
                    <div className="order-2 lg:order-1">
                        <img
                            src={`/storage/${section.image}`}
                            alt={section.title}
                            className="h-48 w-full rounded-lg object-cover shadow-md sm:h-64 lg:h-80"
                        />
                    </div>
                )}
                <div className={`order-1 ${section.image ? 'lg:order-2' : 'lg:col-span-2'}`}>{renderContent(section.content)}</div>
            </div>
        );
    };

    return (
        <>
            <SEOHead
                title={seoMeta?.title}
                description={seoMeta?.description}
                image={seoMeta?.image}
                url={seoMeta?.url}
                type={seoMeta?.type}
                siteName={seoMeta?.siteName}
                locale={seoMeta?.locale}
                structuredData={structuredData}
            />
            <PublicLayout
                title="Profil Desa"
                description={`Profil lengkap ${getVillageProfile()?.name || 'Desa Lemah Duhur'} - sejarah, visi misi, struktur organisasi, dan data demografis`}
            >
                {/* Hero Section */}
                <section className="relative bg-gradient-to-br from-green-50 to-blue-50 py-12 sm:py-16 lg:py-24 dark:from-green-950 dark:to-blue-950">
                    <div className="container mx-auto px-3 sm:px-4 lg:px-8">
                        <div className="mx-auto max-w-4xl text-center">
                            <h1 className="mb-4 text-2xl leading-tight font-bold text-gray-900 sm:mb-6 sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl dark:text-white">
                                Profil Desa
                                <span className="block text-green-600 dark:text-green-400">{getVillageProfile()?.name || 'Lemah Duhur'}</span>
                            </h1>
                            <p className="mb-6 px-4 text-base leading-relaxed text-gray-600 sm:mb-8 sm:px-0 sm:text-lg lg:text-xl dark:text-gray-300">
                                Mengenal lebih dekat sejarah, visi misi, dan struktur pemerintahan {getVillageProfile()?.name || 'Desa Lemah Duhur'},{' '}
                                {getVillageProfile()?.district || 'Kecamatan Caringin'}, {getVillageProfile()?.regency || 'Kabupaten Bogor'}
                            </p>
                            <div className="flex flex-col items-center justify-center gap-3 text-xs text-gray-500 sm:flex-row sm:gap-6 sm:text-sm">
                                <div className="flex items-center">
                                    <MapPin className="mr-2 h-4 w-4 flex-shrink-0" />
                                    <span>
                                        {getVillageProfile()?.district || 'Kecamatan Caringin'}, {getVillageProfile()?.regency || 'Kabupaten Bogor'}
                                    </span>
                                </div>
                                <div className="flex items-center">
                                    <Globe className="mr-2 h-4 w-4 flex-shrink-0" />
                                    <span>{getVillageProfile()?.province || 'Jawa Barat'}, Indonesia</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* History Section */}
                {history.length > 0 && (
                    <section className="bg-white py-12 sm:py-16 dark:bg-gray-900">
                        <div className="container mx-auto px-3 sm:px-4 lg:px-8">
                            <div className="mx-auto max-w-6xl">
                                <div className="mb-8 text-center sm:mb-12">
                                    <div className="mb-3 flex items-center justify-center sm:mb-4">
                                        <History className="mr-2 h-6 w-6 text-green-600 sm:mr-3 sm:h-8 sm:w-8" />
                                        <h2 className="text-2xl font-bold text-gray-900 sm:text-3xl md:text-4xl dark:text-white">Sejarah Desa</h2>
                                    </div>
                                    <p className="mx-auto max-w-2xl px-4 text-base text-gray-600 sm:px-0 sm:text-lg lg:text-xl dark:text-gray-300">
                                        Mengenal asal-usul dan perjalanan sejarah {getVillageProfile()?.name || 'Desa Lemah Duhur'}
                                    </p>
                                </div>

                                <div className="space-y-8 sm:space-y-12">
                                    {history.map((section) => (
                                        <Card key={section.id} className="border-0 shadow-lg dark:bg-gray-800">
                                            <CardHeader className="p-4 pb-4 sm:p-6 sm:pb-6">
                                                <CardTitle className="justify-center text-center text-lg text-green-700 sm:text-xl lg:text-2xl">
                                                    {section.title}
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent className="p-4 pt-0 sm:p-6">{renderImageWithContent(section)}</CardContent>
                                        </Card>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </section>
                )}

                {/* Vision Mission Section */}
                {visionMission.length > 0 && (
                    <section className="bg-gray-50 py-16 dark:bg-gray-950">
                        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="mx-auto max-w-6xl">
                                <div className="mb-12 text-center">
                                    <div className="mb-4 flex items-center justify-center">
                                        <Target className="mr-3 h-8 w-8 text-green-600" />
                                        <h2 className="text-3xl font-bold text-gray-900 md:text-4xl dark:text-white">Visi & Misi</h2>
                                    </div>
                                    <p className="mx-auto max-w-2xl text-xl text-gray-600 dark:text-gray-300">
                                        Arah dan tujuan pembangunan {getVillageProfile()?.name || 'Desa Lemah Duhur'}
                                    </p>
                                </div>

                                <div className="grid grid-rows-1 gap-8">
                                    {visionMission.map((section) => (
                                        <Card key={section.id} className="h-full border-0 shadow-lg dark:bg-gray-800">
                                            <CardHeader className="pb-6">
                                                <CardTitle className="flex items-center justify-center text-2xl text-green-700">
                                                    {section.title}
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                {section.image && (
                                                    <div className="mb-6">
                                                        <img
                                                            src={`/storage/${section.image}`}
                                                            alt={section.title}
                                                            className="h-48 w-full rounded-lg object-cover shadow-md"
                                                        />
                                                    </div>
                                                )}
                                                {renderContent(section.content)}
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </section>
                )}

                {/* Organization Structure Section */}
                {organization.length > 0 && (
                    <section className="bg-white py-16 dark:bg-gray-900">
                        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="mx-auto max-w-6xl">
                                <div className="mb-12 text-center">
                                    <div className="mb-4 flex items-center justify-center">
                                        <Building2 className="mr-3 h-8 w-8 text-green-600" />
                                        <h2 className="text-3xl font-bold text-gray-900 md:text-4xl dark:text-white">Struktur Organisasi</h2>
                                    </div>
                                    <p className="mx-auto max-w-2xl text-xl text-gray-600 dark:text-gray-300">
                                        Perangkat desa dan struktur pemerintahan {getVillageProfile()?.name || 'Desa Lemah Duhur'}
                                    </p>
                                </div>

                                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                                    {organization.map((person) => (
                                        <Card
                                            key={person.id}
                                            className="border-0 text-center shadow-lg transition-shadow duration-300 hover:shadow-xl dark:bg-gray-800"
                                        >
                                            <CardHeader className="pb-4">
                                                {person.image && (
                                                    <div className="mx-auto mb-4">
                                                        <img
                                                            src={`/storage/${person.image}`}
                                                            alt={person.title}
                                                            className="mx-auto h-40 w-30 rounded-full object-cover shadow-md"
                                                        />
                                                    </div>
                                                )}
                                                <CardTitle className="justify-center text-center text-lg text-gray-900 dark:text-white">
                                                    {person.title}
                                                </CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                <Badge variant="secondary" className="mb-3">
                                                    {person.section === 'organization' ? 'Perangkat Desa' : person.section}
                                                </Badge>
                                                {person.content && (
                                                    <div className="text-sm text-gray-600 dark:text-gray-300">{renderContent(person.content)}</div>
                                                )}
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </section>
                )}

                {/* Demographics Section */}
                {demographics.length > 0 && (
                    <section className="bg-gray-50 py-16 dark:bg-gray-950">
                        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="mx-auto max-w-6xl">
                                <div className="mb-12 text-center">
                                    <div className="mb-4 flex items-center justify-center">
                                        <Users className="mr-3 h-8 w-8 text-green-600" />
                                        <h2 className="text-3xl font-bold text-gray-900 md:text-4xl dark:text-white">Data Demografis</h2>
                                    </div>
                                    <p className="mx-auto max-w-2xl text-xl text-gray-600 dark:text-gray-300">
                                        Statistik kependudukan dan sosial ekonomi {getVillageProfile()?.name || 'Desa Lemah Duhur'}
                                    </p>
                                </div>

                                <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
                                    {demographics.map((section) => (
                                        <Card key={section.id} className="border-0 shadow-lg dark:bg-gray-800">
                                            <CardHeader className="pb-6">
                                                <CardTitle className="justify-center text-center text-2xl text-green-700">{section.title}</CardTitle>
                                            </CardHeader>
                                            <CardContent>
                                                {section.image && (
                                                    <div className="mb-6">
                                                        <img
                                                            src={`/storage/${section.image}`}
                                                            alt={section.title}
                                                            className="h-64 w-full rounded-lg object-cover shadow-md"
                                                        />
                                                    </div>
                                                )}
                                                {renderContent(section.content)}
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </section>
                )}

                {/* Geography Section */}
                {geography.length > 0 && (
                    <section className="bg-white py-16 dark:bg-gray-900">
                        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="mx-auto max-w-6xl">
                                <div className="mb-12 text-center">
                                    <div className="mb-4 flex items-center justify-center">
                                        <MapPin className="mr-3 h-8 w-8 text-green-600" />
                                        <h2 className="text-3xl font-bold text-gray-900 md:text-4xl dark:text-white">Data Geografis</h2>
                                    </div>
                                    <p className="mx-auto max-w-2xl text-xl text-gray-600 dark:text-gray-300">
                                        Kondisi geografis dan batas wilayah {getVillageProfile()?.name || 'Desa Lemah Duhur'}
                                    </p>
                                </div>

                                <div className="space-y-8">
                                    {geography.map((section) => (
                                        <Card key={section.id} className="border-0 shadow-lg dark:bg-gray-800">
                                            <CardHeader className="pb-6">
                                                <CardTitle className="text-2xl text-green-700">{section.title}</CardTitle>
                                            </CardHeader>
                                            <CardContent>{renderImageWithContent(section)}</CardContent>
                                        </Card>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </section>
                )}

                {/* Contact Information */}
                <section className="bg-green-600 py-16 text-white dark:bg-green-900 dark:text-green-100">
                    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="mx-auto max-w-4xl text-center">
                            <h2 className="mb-8 text-3xl font-bold md:text-4xl dark:text-green-100">Hubungi Kami</h2>
                            <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
                                <div className="flex flex-col items-center">
                                    <Phone className="mb-4 h-8 w-8" />
                                    <h3 className="mb-2 text-xl font-semibold dark:text-green-100">Telepon</h3>
                                    <p className="text-green-100">{getContactInfo()?.phone || 'Belum tersedia'}</p>
                                </div>
                                <div className="flex flex-col items-center">
                                    <Mail className="mb-4 h-8 w-8" />
                                    <h3 className="mb-2 text-xl font-semibold dark:text-green-100">Email</h3>
                                    <p className="text-green-100">{getContactInfo()?.email || 'Belum tersedia'}</p>
                                </div>
                                <div className="flex flex-col items-center">
                                    <MapPin className="mb-4 h-8 w-8" />
                                    <h3 className="mb-2 text-xl font-semibold dark:text-green-100">Alamat</h3>
                                    <p className="text-green-100">
                                        {getContactInfo()?.address || 'Kantor Desa Lemah Duhur, Kec. Caringin, Kab. Bogor, Jawa Barat'}
                                        {getContactInfo()?.postal_code && ` Kode Pos ${getContactInfo()?.postal_code}`}
                                    </p>
                                </div>
                            </div>

                            <Separator className="my-8 bg-green-500" />

                            <div className="flex items-center justify-center space-x-6 text-sm text-green-100">
                                <div className="flex items-center dark:text-green-200">
                                    <Calendar className="mr-2 h-4 w-4" />
                                    <span>{getOperatingHours()?.weekdays || 'Senin - Jumat: 08:00 - 16:00 WIB'}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </PublicLayout>
        </>
    );
}
