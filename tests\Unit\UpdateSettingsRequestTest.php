<?php

use App\Http\Requests\Settings\UpdateSettingsRequest;
use App\Models\User;
use Illuminate\Support\Facades\Validator;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('validation passes with valid village contact info', function () {
    $validData = [
        'village' => [
            'contact_info' => [
                'phone' => '(0251) 8240123',
                'whatsapp' => '0',
                'email' => '<EMAIL>',
                'address' => 'Kantor <PERSON>, Kec. Caringin, Kab. Bogor, Jawa Barat',
                'postal_code' => '16730',
                'maps_link' => 'https://maps.app.goo.gl/F2SWrp4QBYBJXiG6A',
            ],
            'profile' => [
                'name' => 'Desa Lemah Duhur',
                'district' => 'Kecamatan Caringin',
                'regency' => 'Kabupaten Bogor',
                'province' => 'Jawa Barat',
                'established_year' => '1910-1920',
                'area' => 'Dataran tinggi pegunungan',
                'population' => 'Data akan diperbarui sesuai sensus terbaru',
            ],
            'operating_hours' => [
                'weekdays' => 'Senin - Jumat: 08:00 - 15:00 WIB',
                'saturday' => 'Sabtu: 08:00 - 12:00 WIB',
                'sunday' => 'Minggu: Tutup',
                'break' => 'Istirahat: 12:00 - 13:00 WIB',
                'holidays' => 'Hari libur nasional: Tutup',
            ],
            'emergency_contacts' => [
                'village_head' => [
                    'title' => 'Kepala Desa',
                    'phone' => '081234567891',
                ],
                'village_secretary' => [
                    'title' => 'Sekretaris Desa',
                    'phone' => '081234567892',
                ],
                'security' => [
                    'title' => 'Keamanan Desa (Hansip)',
                    'phone' => '081234567893',
                ],
                'health_center' => [
                    'title' => 'Puskesmas Caringin',
                    'phone' => '(0251) 8240456',
                ],
            ],
        ],
    ];

    $request = new UpdateSettingsRequest;
    $validator = Validator::make($validData, $request->rules(), $request->messages());

    expect($validator->passes())->toBeTrue();
});

test('validation fails with invalid phone number format', function () {
    $invalidData = [
        'village' => [
            'contact_info' => [
                'phone' => 'invalid-phone',
                'whatsapp' => '0',
                'email' => '<EMAIL>',
                'address' => 'Kantor Desa Lemah Duhur, Kec. Caringin, Kab. Bogor, Jawa Barat',
                'postal_code' => '16730',
            ],
        ],
    ];

    $request = new UpdateSettingsRequest;
    $validator = Validator::make($invalidData, $request->rules(), $request->messages());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('village.contact_info.phone'))->toBeTrue();
});

test('validation fails with invalid email format', function () {
    $invalidData = [
        'village' => [
            'contact_info' => [
                'phone' => '(0251) 8240123',
                'whatsapp' => '0',
                'email' => 'invalid-email',
                'address' => 'Kantor Desa Lemah Duhur, Kec. Caringin, Kab. Bogor, Jawa Barat',
                'postal_code' => '16730',
            ],
        ],
    ];

    $request = new UpdateSettingsRequest;
    $validator = Validator::make($invalidData, $request->rules(), $request->messages());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('village.contact_info.email'))->toBeTrue();
});

test('validation fails with invalid postal code format', function () {
    $invalidData = [
        'village' => [
            'contact_info' => [
                'phone' => '(0251) 8240123',
                'whatsapp' => '0',
                'email' => '<EMAIL>',
                'address' => 'Kantor Desa Lemah Duhur, Kec. Caringin, Kab. Bogor, Jawa Barat',
                'postal_code' => '1673', // Invalid: should be 5 digits
            ],
        ],
    ];

    $request = new UpdateSettingsRequest;
    $validator = Validator::make($invalidData, $request->rules(), $request->messages());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('village.contact_info.postal_code'))->toBeTrue();
});

test('validation fails with invalid whatsapp number format', function () {
    $invalidData = [
        'village' => [
            'contact_info' => [
                'phone' => '(0251) 8240123',
                'whatsapp' => '071234567890', // Invalid: should start with 08 or +62
                'email' => '<EMAIL>',
                'address' => 'Kantor Desa Lemah Duhur, Kec. Caringin, Kab. Bogor, Jawa Barat',
                'postal_code' => '16730',
            ],
        ],
    ];

    $request = new UpdateSettingsRequest;
    $validator = Validator::make($invalidData, $request->rules(), $request->messages());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('village.contact_info.whatsapp'))->toBeTrue();
});

test('validation requires mandatory fields', function () {
    $incompleteData = [
        'village' => [
            'contact_info' => [
                // Missing required fields
                'maps_link' => 'https://maps.app.goo.gl/F2SWrp4QBYBJXiG6A',
            ],
        ],
    ];

    $request = new UpdateSettingsRequest;
    $validator = Validator::make($incompleteData, $request->rules(), $request->messages());

    expect($validator->fails())->toBeTrue();
    expect($validator->errors()->has('village.contact_info.phone'))->toBeTrue();
    expect($validator->errors()->has('village.contact_info.whatsapp'))->toBeTrue();
    expect($validator->errors()->has('village.contact_info.email'))->toBeTrue();
    expect($validator->errors()->has('village.contact_info.address'))->toBeTrue();
    expect($validator->errors()->has('village.contact_info.postal_code'))->toBeTrue();
});

test('authorization allows admin users', function () {
    $adminUser = User::factory()->create(['role' => 'admin']);

    $request = new UpdateSettingsRequest;
    $request->setUserResolver(fn () => $adminUser);

    expect($request->authorize())->toBeTrue();
});

test('authorization denies non-admin users', function () {
    $regularUser = User::factory()->create(['role' => 'user']);

    $request = new UpdateSettingsRequest;
    $request->setUserResolver(fn () => $regularUser);

    expect($request->authorize())->toBeFalse();
});

test('authorization denies unauthenticated users', function () {
    $request = new UpdateSettingsRequest;
    $request->setUserResolver(fn () => null);

    expect($request->authorize())->toBeFalse();
});
