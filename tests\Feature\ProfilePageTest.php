<?php

use App\Models\Profile;

describe('Profile Page Feature', function () {
    beforeEach(function () {
        // Create profile data for different sections
        Profile::factory()->history()->create([
            'title' => 'Sejarah <PERSON>a Lemah Duhur',
            'content' => 'Desa Lemah Duhur didirikan pada tahun 1910-1920...',
            'order' => 1,
        ]);

        Profile::factory()->visionMission()->create([
            'title' => 'Visi Desa',
            'content' => 'Menjadi desa yang maju dan sejahtera...',
            'order' => 1,
        ]);

        Profile::factory()->visionMission()->create([
            'title' => 'Misi Desa',
            'content' => '1. Meningkatkan kesejahteraan masyarakat...',
            'order' => 2,
        ]);

        Profile::factory()->organization()->count(3)->create();
        Profile::factory()->demographics()->create([
            'title' => 'Data Penduduk',
            'content' => 'Jumlah penduduk: 5000 jiwa...',
        ]);

        Profile::factory()->geography()->create([
            'title' => 'Lokasi Desa',
            'content' => 'Desa Lemah Duhur terletak di Kecamatan Caringin...',
        ]);
    });

    it('displays profile page successfully', function () {
        $response = $this->get('/profil');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Public/Profile')
        );
    });

    it('shows history section', function () {
        $response = $this->get('/profil');

        $response->assertInertia(fn ($page) => $page->has('history')
            ->where('history', fn ($history) => count($history) > 0
            )
        );
    });

    it('shows vision and mission section', function () {
        $response = $this->get('/profil');

        $response->assertInertia(fn ($page) => $page->has('visionMission')
            ->where('visionMission', fn ($visionMission) => count($visionMission) >= 2 // Should have both vision and mission
            )
        );
    });

    it('shows organization structure', function () {
        $response = $this->get('/profil');

        $response->assertInertia(fn ($page) => $page->has('organization')
            ->where('organization', fn ($organization) => count($organization) > 0
            )
        );
    });

    it('shows demographics data', function () {
        $response = $this->get('/profil');

        $response->assertInertia(fn ($page) => $page->has('demographics')
            ->where('demographics', fn ($demographics) => count($demographics) > 0
            )
        );
    });

    it('shows geography information', function () {
        $response = $this->get('/profil');

        $response->assertInertia(fn ($page) => $page->has('geography')
            ->where('geography', fn ($geography) => count($geography) > 0
            )
        );
    });

    it('orders sections correctly', function () {
        $response = $this->get('/profil');

        $response->assertInertia(fn ($page) => $page->where('visionMission', function ($visionMission) {
            // Convert Collection to array if needed
            if ($visionMission instanceof \Illuminate\Support\Collection) {
                $visionMission = $visionMission->toArray();
            }

            // Check if items are ordered correctly
            $orders = array_column($visionMission, 'order');
            $sortedOrders = array_values(\Illuminate\Support\Arr::sort($orders));

            return $orders === $sortedOrders;
        })
        );
    });

    it('includes SEO meta tags for profile page', function () {
        $response = $this->get('/profil');

        $response->assertInertia(fn ($page) => $page->has('seo')
            ->has('seo.title')
            ->has('seo.description')
            ->where('seo.title', fn ($title) => str_contains($title, 'Profil')
            )
        );
    });

    it('handles missing sections gracefully', function () {
        // Delete all profiles
        Profile::query()->delete();

        $response = $this->get('/profil');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Public/Profile')
            ->has('history')
            ->has('visionMission')
            ->has('organization')
            ->has('demographics')
            ->has('geography')
        );
    });

    it('displays images when available', function () {
        Profile::factory()->organization()->withImage()->create([
            'title' => 'Kepala Desa',
            'content' => 'Bapak John Doe',
        ]);

        $response = $this->get('/profil');

        $response->assertInertia(fn ($page) => $page->where('organization', function ($organization) {
            foreach ($organization as $member) {
                if ($member['title'] === 'Kepala Desa' && ! empty($member['image'])) {
                    return true;
                }
            }

            return false;
        })
        );
    });

    describe('Content Validation', function () {
        it('contains village name in content', function () {
            $response = $this->get('/profil');

            $response->assertInertia(fn ($page) => $page->where('history', function ($history) {
                foreach ($history as $item) {
                    if (str_contains($item['content'], 'Lemah Duhur')) {
                        return true;
                    }
                }

                return false;
            })
            );
        });

        it('includes proper Indonesian content', function () {
            $response = $this->get('/profil');

            $response->assertInertia(fn ($page) => $page->where('visionMission', function ($visionMission) {
                foreach ($visionMission as $item) {
                    // Check for Indonesian words
                    if (str_contains($item['title'], 'Visi') || str_contains($item['title'], 'Misi')) {
                        return true;
                    }
                }

                return false;
            })
            );
        });
    });

    describe('Performance', function () {
        it('loads efficiently with multiple sections', function () {
            \DB::enableQueryLog();

            $response = $this->get('/profil');

            $queries = \DB::getQueryLog();

            $response->assertStatus(200);
            // Should use efficient queries to load all sections
            expect(count($queries))->toBeLessThan(12);
        });
    });

    describe('Accessibility', function () {
        it('includes proper structure for screen readers', function () {
            $response = $this->get('/profil');

            $response->assertInertia(fn ($page) => $page->has('seo.title')
                ->has('seo.description')
            );
        });
    });
});
