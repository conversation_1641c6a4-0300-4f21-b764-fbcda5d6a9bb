<?php

use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\ImageUploadController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\PotentialController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\PublicController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\SitemapController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', [PublicController::class, 'home'])->name('home');

Route::get('/profil', [ProfileController::class, 'index'])->name('profile');
Route::get('/layanan', [ServiceController::class, 'index'])->name('services.index');
Route::get('/layanan/{service}', [ServiceController::class, 'show'])->name('services.show');
Route::get('/berita', [NewsController::class, 'index'])->name('news.index');
Route::get('/berita/{slug}', [NewsController::class, 'show'])->name('news.show');
Route::get('/potensi', [PotentialController::class, 'index'])->name('potential.index');
Route::get('/potensi/{potential}', [PotentialController::class, 'show'])->name('potential.show');

// Complaint Routes
Route::prefix('pengaduan')->name('complaints.')->group(function () {
    Route::get('/', [App\Http\Controllers\ComplaintController::class, 'index'])->name('index');
    Route::get('/publik', [App\Http\Controllers\ComplaintController::class, 'publicIndex'])->name('public');
    Route::post('/', [App\Http\Controllers\ComplaintController::class, 'store'])->name('store');
    Route::get('/berhasil', [App\Http\Controllers\ComplaintController::class, 'success'])->name('success');
    Route::get('/lacak', [App\Http\Controllers\ComplaintController::class, 'tracking'])->name('tracking');
    Route::post('/lacak', [App\Http\Controllers\ComplaintController::class, 'track'])->name('track');
});

// SEO Routes
Route::get('/sitemap.xml', [SitemapController::class, 'index'])->name('sitemap');
Route::get('/robots.txt', [SitemapController::class, 'robots'])->name('robots');

// Error Pages
Route::get('/errors/403', function () {
    return Inertia::render('Errors/403');
})->name('errors.403');

Route::get('/errors/404', function () {
    return Inertia::render('Errors/404');
})->name('errors.404');

Route::get('/errors/500', function () {
    return Inertia::render('Errors/500');
})->name('errors.500');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
});

// Admin routes with admin middleware
Route::middleware(['auth', 'verified', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Image Upload Routes (available to all authenticated users)
    Route::prefix('api/images')->name('api.images.')->group(function () {
        Route::post('/upload', [ImageUploadController::class, 'upload'])->name('upload');
        Route::post('/upload-multiple', [ImageUploadController::class, 'uploadMultiple'])->name('upload-multiple');
        Route::delete('/delete', [ImageUploadController::class, 'delete'])->name('delete');
        Route::post('/placeholder', [ImageUploadController::class, 'generatePlaceholder'])->name('placeholder');
    });
});

// Admin routes with admin middleware
Route::middleware(['auth', 'verified', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Admin News Management
    Route::prefix('news')->name('news.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\NewsController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Admin\NewsController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Admin\NewsController::class, 'store'])->name('store');
        Route::get('/{news}', [App\Http\Controllers\Admin\NewsController::class, 'show'])->name('show');
        Route::get('/{news}/edit', [App\Http\Controllers\Admin\NewsController::class, 'edit'])->name('edit');
        Route::put('/{news}', [App\Http\Controllers\Admin\NewsController::class, 'update'])->name('update');
        Route::delete('/{news}', [App\Http\Controllers\Admin\NewsController::class, 'destroy'])->name('destroy');
        Route::post('/{news}/toggle-publish', [App\Http\Controllers\Admin\NewsController::class, 'togglePublish'])->name('toggle-publish');
        Route::get('/{news}/preview', [App\Http\Controllers\Admin\NewsController::class, 'preview'])->name('preview');
        Route::post('/bulk-publish', [App\Http\Controllers\Admin\NewsController::class, 'bulkPublish'])->name('bulk-publish');
        Route::post('/bulk-unpublish', [App\Http\Controllers\Admin\NewsController::class, 'bulkUnpublish'])->name('bulk-unpublish');
        Route::post('/bulk-delete', [App\Http\Controllers\Admin\NewsController::class, 'bulkDelete'])->name('bulk-delete');
    });

    // Admin Services Management
    Route::prefix('services')->name('services.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\ServiceController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Admin\ServiceController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Admin\ServiceController::class, 'store'])->name('store');
        Route::get('/{service}', [App\Http\Controllers\Admin\ServiceController::class, 'show'])->name('show');
        Route::get('/{service}/edit', [App\Http\Controllers\Admin\ServiceController::class, 'edit'])->name('edit');
        Route::put('/{service}', [App\Http\Controllers\Admin\ServiceController::class, 'update'])->name('update');
        Route::delete('/{service}', [App\Http\Controllers\Admin\ServiceController::class, 'destroy'])->name('destroy');
        Route::post('/{service}/toggle-status', [App\Http\Controllers\Admin\ServiceController::class, 'toggleStatus'])->name('toggle-status');
        Route::post('/{service}/activate', [App\Http\Controllers\Admin\ServiceController::class, 'activate'])->name('activate');
        Route::post('/{service}/deactivate', [App\Http\Controllers\Admin\ServiceController::class, 'deactivate'])->name('deactivate');
        Route::post('/bulk-activate', [App\Http\Controllers\Admin\ServiceController::class, 'bulkActivate'])->name('bulk-activate');
        Route::post('/bulk-deactivate', [App\Http\Controllers\Admin\ServiceController::class, 'bulkDeactivate'])->name('bulk-deactivate');
        Route::post('/bulk-delete', [App\Http\Controllers\Admin\ServiceController::class, 'bulkDelete'])->name('bulk-delete');
    });

    // Admin Potentials Management
    Route::prefix('potentials')->name('potentials.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\PotentialController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Admin\PotentialController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Admin\PotentialController::class, 'store'])->name('store');
        Route::get('/{potential}', [App\Http\Controllers\Admin\PotentialController::class, 'show'])->name('show');
        Route::get('/{potential}/edit', [App\Http\Controllers\Admin\PotentialController::class, 'edit'])->name('edit');
        Route::put('/{potential}', [App\Http\Controllers\Admin\PotentialController::class, 'update'])->name('update');
        Route::delete('/{potential}', [App\Http\Controllers\Admin\PotentialController::class, 'destroy'])->name('destroy');
        Route::post('/{potential}/toggle-featured', [App\Http\Controllers\Admin\PotentialController::class, 'toggleFeatured'])->name('toggle-featured');
    });

    // Admin Profiles Management
    Route::prefix('profiles')->name('profiles.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\ProfileController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Admin\ProfileController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Admin\ProfileController::class, 'store'])->name('store');
        Route::get('/{profile}', [App\Http\Controllers\Admin\ProfileController::class, 'show'])->name('show');
        Route::get('/{profile}/edit', [App\Http\Controllers\Admin\ProfileController::class, 'edit'])->name('edit');
        Route::put('/{profile}', [App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('update');
        Route::delete('/{profile}', [App\Http\Controllers\Admin\ProfileController::class, 'destroy'])->name('destroy');
        Route::get('/section/{section}', [App\Http\Controllers\Admin\ProfileController::class, 'manageSection'])->name('section');
        Route::post('/section/{section}/order', [App\Http\Controllers\Admin\ProfileController::class, 'updateOrder'])->name('section.order');
    });

    // Content management routes
    Route::get('/services/manage', function () {
        return redirect()->route('admin.services.index');
    })->name('services.manage');

    Route::get('/potentials/manage', function () {
        return redirect()->route('admin.potentials.index');
    })->name('potentials.manage');

    Route::get('/profile/manage', function () {
        return redirect()->route('admin.profiles.index');
    })->name('profile.manage');

    // Admin Complaints Management
    Route::prefix('complaints')->name('complaints.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\ComplaintController::class, 'index'])->name('index');
        Route::get('/{complaint}', [App\Http\Controllers\Admin\ComplaintController::class, 'show'])->name('show');
        Route::put('/{complaint}/status', [App\Http\Controllers\Admin\ComplaintController::class, 'updateStatus'])->name('update-status');
        Route::post('/{complaint}/respond', [App\Http\Controllers\Admin\ComplaintController::class, 'respond'])->name('respond');
        Route::post('/bulk-update', [App\Http\Controllers\Admin\ComplaintController::class, 'bulkUpdate'])->name('bulk-update');
        Route::get('/export/excel', [App\Http\Controllers\Admin\ComplaintController::class, 'exportExcel'])->name('export.excel');
        Route::get('/export/pdf', [App\Http\Controllers\Admin\ComplaintController::class, 'exportPdf'])->name('export.pdf');
    });

    // Admin Settings Management
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\SettingController::class, 'index'])->name('index');
        Route::put('/', [App\Http\Controllers\Admin\SettingController::class, 'update'])->name('update');
    });

    // Admin User Management
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\UserController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Admin\UserController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Admin\UserController::class, 'store'])->name('store');
        Route::get('/{user}', [App\Http\Controllers\Admin\UserController::class, 'show'])->name('show');
        Route::get('/{user}/edit', [App\Http\Controllers\Admin\UserController::class, 'edit'])->name('edit');
        Route::put('/{user}', [App\Http\Controllers\Admin\UserController::class, 'update'])->name('update');
        Route::delete('/{user}', [App\Http\Controllers\Admin\UserController::class, 'destroy'])->name('destroy');
        Route::post('/{user}/toggle-role', [App\Http\Controllers\Admin\UserController::class, 'toggleRole'])->name('toggle-role');
        Route::post('/bulk-delete', [App\Http\Controllers\Admin\UserController::class, 'bulkDelete'])->name('bulk-delete');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
