import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    [key: string]: unknown;
}

export interface User {
    id: number;
    name: string;
    email: string;
    role: 'admin' | 'user';
    avatar?: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    [key: string]: unknown; // This allows for additional properties...
}

export interface ProfileSection {
    id: number;
    section: string;
    title: string;
    content: string;
    image: string | null;
    order: number;
}

export interface News {
    id: number;
    title: string;
    slug: string;
    content: string;
    excerpt: string;
    featured_image: string | null;
    category: string;
    published_at: string;
    is_published: boolean;
    meta_title: string;
    meta_description: string;
    created_at: string;
    updated_at: string;
}

export interface PaginatedNews {
    data: News[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    links: PaginationLink[];
}

export interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

export interface PaginatedData<T> {
    data: T[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
    links: PaginationLink[];
}

// Re-export settings types for convenience
export type {
    AnySetting,
    ContactInfo,
    EmergencyContact,
    EmergencyContacts,
    OperatingHours,
    ServiceSettings,
    SettingKey,
    SettingValue,
    VillageHistory,
    VillageProfile,
    VillageSettings,
} from './settings';
